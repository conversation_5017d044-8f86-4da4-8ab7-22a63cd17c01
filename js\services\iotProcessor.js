const { listThings } = require('../modules/awsIot');
const { mqtt, iot, auth } = require('aws-iot-device-sdk-v2');
const Region = require('../models/Region');
const { processIotGpsMessage } = require('./gps');
const { processIotAisMessage } = require('./ais');
const { postLogToSlack } = require('../modules/notifyLog');

const AWS_ACCESS_KEY_ID = process.env.AWS_ACCESS_KEY_ID
const AWS_SECRET_ACCESS_KEY = process.env.AWS_SECRET_ACCESS_KEY
const MQTT_CLIENT_ID = process.env.MQTT_CLIENT_ID
const MQTT_BROKER = process.env.MQTT_BROKER

const clients = {}
// var subscribedThings = []

initializeClients()

setInterval(() => {
    console.log('[IoT Service] Reinitializing clients...');
    initializeClients()
}, 3600000);

function initializeClients() {
    Region.find({ is_live: true }).then((regions) => {
        regions.forEach(async (region) => {
            if (clients[region.value]) {
                console.log('[IoT Service] Already connected to region', region.value);
                return;
            }

            // const region = data.region;
            // const things = data.things.filter(
            //     (thing) => thing.thingTypeName === 'smartmast'
            // );

            // if (things.length === 0) return console.log(`No applicable things in region ${region}`);

            // const thingArns = things.map(t => t.thingArn)
            // if (thingArns.every(arn => subscribedThings.includes(arn))) return console.log('Already subscribed to things in region', region);

            // subscribedThings = subscribedThings.concat(thingArns)
            // console.log(subscribedThings)

            // const oldClient = clients[region]
            // if (oldClient) {
            //     oldClient.removeAllListeners()
            //     await oldClient.disconnect()
            // }

            const config = iot.AwsIotMqttConnectionConfigBuilder.new_with_websockets({
                region: region.value,
                credentials_provider: auth.AwsCredentialsProvider.newStatic(
                    AWS_ACCESS_KEY_ID,
                    AWS_SECRET_ACCESS_KEY
                ),
            })
                .with_clean_session(true)
                .with_client_id(`${MQTT_CLIENT_ID}-${region.value}`)
                .with_endpoint(`${MQTT_BROKER}.${region.value}.amazonaws.com`)
                .build();

            const client = new mqtt.MqttClient();
            const mqttClient = client.new_connection(config);

            try {
                await mqttClient.connect();
                clients[region.value] = mqttClient;
                console.log(`[IoT Service] Connected to MQTT Broker in region ${region.value}`);
                setupMqttClientListeners(mqttClient, region.value);
                // subscribeToGps(mqttClient, things, region);
                mqttClient.subscribe('#', mqtt.QoS.AtMostOnce)
            } catch (err) {
                console.error(`[IoT Service] Failed to connect to MQTT Broker in region ${region.value}: ${err}`);
            }
        });
    }).catch((err) => {
        console.error(`[IoT Service] FATAL ERROR: could not get live regions ${err}`);
    });

    // if (process.env.NODE_ENV === 'prod') {
    //     listThings().then(async (regions) => {
    //         const sensorNames = regions.map(r => r.things.filter(e => e.thingTypeName).map(e => e.thingName)).flat();
    //         thingsboardService.verifySensors(sensorNames)
    //     }).catch((err) => {
    //         console.error(`[IoT Service] FATAL ERROR: could not get list things ${err}`);
    //     });
    // }
}

function setupMqttClientListeners(mqttClient, region) {
    mqttClient.on('closed', () => {
        console.log(`[IoT Service] Connection closed for MQTT Broker in region ${region}`);
    });

    mqttClient.on('interrupt', (err) => {
        console.log(`[IoT Service] Received interrupt for connection to MQTT Broker in region ${region}: ${err}`);
    });

    mqttClient.on('resume', () => {
        console.log(`[IoT Service] Resumed connection to MQTT Broker in region ${region}`);
        // subscribeToGps(mqttClient, things, region);
        mqttClient.subscribe('#', mqtt.QoS.AtMostOnce)
    });

    mqttClient.on('disconnect', () => {
        console.log(`[IoT Service] FATAL ERROR: Disconnected from MQTT Broker in region ${region}`);
        delete clients[region];
        mqttClient.removeAllListeners();
        initializeClients();
    });

    mqttClient.on('error', (err) => {
        console.error(`[IoT Service] Received connection error for MQTT Broker in region ${region}: ${err}`);
    });

    mqttClient.on('message', async (topic, message) => {
        // console.log('[IoT Service] received message from topic', topic, 'in region', region)
        await processMessage(topic, message, region);
    });
}

// function subscribeToGps(mqttClient, things, region) {
//     things.forEach((thing) => {
//         const topic = `${thing.thingName}/gps/status`;
//         mqttClient
//             .subscribe(topic, mqtt.QoS.AtLeastOnce)
//             .then((res) => {
//                 if (res.error_code) {
//                     console.error(`Failed to subscribe to topic ${topic} in region ${region}: error_code ${res.error_code}`);
//                 } else {
//                     console.log(`Subscribed to topic ${topic} in region ${region}`);
//                 }
//             })
//             .catch((err) =>
//                 console.error(`Failed to subscribe to topic ${topic} in region ${region}: ${err}`)
//             );
//     });
// }

const slackLoggerTimeout = 60 * 60 * 1000;
let lastSlackLoggedError = null;

async function processMessage(topic, message, region) {
    try {
        if (topic.endsWith('/gps/status')) {
            await processIotGpsMessage(topic, message, region);
        } else if (topic.endsWith('/sdr/status')) {
            await processIotAisMessage(topic, message, region);
        }
    } catch (error) {
        if (!lastSlackLoggedError || lastSlackLoggedError.getTime() + slackLoggerTimeout < new Date().getTime()) {
            lastSlackLoggedError = new Date();
            postLogToSlack({
                severity: 'fatal',
                message: 'Error while processing IoT MQTT message',
                stack: error.stack
            });
        }
        console.error(`[IoT Service] FATAL ERROR: error while processing MQTT message for topic ${topic} in region ${region}: ${error}`);
    }
}