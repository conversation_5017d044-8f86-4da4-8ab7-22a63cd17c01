// Test script to verify getLocationsCollections UTC compatibility
// node scripts/testGetLocationsCollectionsUTC.js

require('dotenv').config();
const { getLocationsCollections } = require('../utils/functions');

// Mock database object to simulate the locations database
const mockDb = {
    db: {
        listCollections: () => ({
            toArray: async () => [
                { name: '2025-08' },
                { name: '2025-09' },
                { name: '2025-10' },
                { name: '2025-11' },
                { name: 'system.indexes' }, // Should be filtered out
                { name: 'invalid-date' }, // Should be filtered out
                { name: '2024-12' },
                { name: '2025-01' }
            ]
        })
    },
    collection: (name) => ({ name: `collection_${name}` })
};

async function testUTCCompatibility() {
    console.log('🧪 Testing getLocationsCollections UTC compatibility\n');
    
    // Test case 1: September 2025 full month
    console.log('Test 1: Full September 2025 (UTC)');
    console.log('Input:');
    console.log('  start: 2025-09-01T00:00:00.000Z');
    console.log('  end:   2025-09-30T23:59:59.999Z');
    
    const start1 = '2025-09-01T00:00:00.000Z';
    const end1 = '2025-09-30T23:59:59.999Z';
    
    try {
        const collections1 = await getLocationsCollections(mockDb, start1, end1);
        console.log('Expected: ["2025-09"]');
        console.log('Actual:  ', collections1.map(c => c.name.replace('collection_', '')));
        console.log('✅ Test 1:', collections1.length === 1 && collections1[0].name === 'collection_2025-09' ? 'PASSED' : 'FAILED');
    } catch (error) {
        console.log('❌ Test 1 FAILED with error:', error.message);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test case 2: Cross-month range (Aug-Oct 2025)
    console.log('Test 2: Cross-month range (Aug-Oct 2025)');
    console.log('Input:');
    console.log('  start: 2025-08-15T12:30:00.000Z');
    console.log('  end:   2025-10-15T18:45:00.000Z');
    
    const start2 = '2025-08-15T12:30:00.000Z';
    const end2 = '2025-10-15T18:45:00.000Z';
    
    try {
        const collections2 = await getLocationsCollections(mockDb, start2, end2);
        const expectedCollections = ['2025-08', '2025-09', '2025-10'];
        const actualCollections = collections2.map(c => c.name.replace('collection_', '')).sort();
        console.log('Expected:', expectedCollections);
        console.log('Actual:  ', actualCollections);
        console.log('✅ Test 2:', JSON.stringify(actualCollections) === JSON.stringify(expectedCollections) ? 'PASSED' : 'FAILED');
    } catch (error) {
        console.log('❌ Test 2 FAILED with error:', error.message);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test case 3: Single day in September (should still return September collection)
    console.log('Test 3: Single day in September');
    console.log('Input:');
    console.log('  start: 2025-09-15T10:00:00.000Z');
    console.log('  end:   2025-09-15T14:00:00.000Z');
    
    const start3 = '2025-09-15T10:00:00.000Z';
    const end3 = '2025-09-15T14:00:00.000Z';
    
    try {
        const collections3 = await getLocationsCollections(mockDb, start3, end3);
        console.log('Expected: ["2025-09"]');
        console.log('Actual:  ', collections3.map(c => c.name.replace('collection_', '')));
        console.log('✅ Test 3:', collections3.length === 1 && collections3[0].name === 'collection_2025-09' ? 'PASSED' : 'FAILED');
    } catch (error) {
        console.log('❌ Test 3 FAILED with error:', error.message);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test case 4: Test with different system timezones (simulate by checking dayjs behavior)
    console.log('Test 4: Timezone independence verification');
    console.log('Testing that the function uses UTC regardless of system timezone');
    
    // Test the same September range but verify it works consistently
    const start4 = '2025-09-01T00:00:00.000Z';
    const end4 = '2025-09-30T23:59:59.999Z';
    
    // Show current system timezone for reference
    const now = new Date();
    console.log('System timezone offset:', now.getTimezoneOffset(), 'minutes from UTC');
    console.log('System timezone:', Intl.DateTimeFormat().resolvedOptions().timeZone);
    
    try {
        const collections4 = await getLocationsCollections(mockDb, start4, end4);
        console.log('Input (same as Test 1):');
        console.log('  start: 2025-09-01T00:00:00.000Z');
        console.log('  end:   2025-09-30T23:59:59.999Z');
        console.log('Expected: ["2025-09"]');
        console.log('Actual:  ', collections4.map(c => c.name.replace('collection_', '')));
        console.log('✅ Test 4:', collections4.length === 1 && collections4[0].name === 'collection_2025-09' ? 'PASSED' : 'FAILED');
    } catch (error) {
        console.log('❌ Test 4 FAILED with error:', error.message);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test case 5: Edge case - end of month boundary
    console.log('Test 5: Month boundary edge case');
    console.log('Input:');
    console.log('  start: 2025-08-31T23:59:59.999Z');
    console.log('  end:   2025-09-01T00:00:00.001Z');
    
    const start5 = '2025-08-31T23:59:59.999Z';
    const end5 = '2025-09-01T00:00:00.001Z';
    
    try {
        const collections5 = await getLocationsCollections(mockDb, start5, end5);
        const expectedCollections = ['2025-08', '2025-09'];
        const actualCollections = collections5.map(c => c.name.replace('collection_', '')).sort();
        console.log('Expected:', expectedCollections);
        console.log('Actual:  ', actualCollections);
        console.log('✅ Test 5:', JSON.stringify(actualCollections) === JSON.stringify(expectedCollections) ? 'PASSED' : 'FAILED');
    } catch (error) {
        console.log('❌ Test 5 FAILED with error:', error.message);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Test case 6: No timestamps (should return all collections)
    console.log('Test 6: No timestamps provided (should return all collections)');
    
    try {
        const collections6 = await getLocationsCollections(mockDb, null, null);
        const expectedCount = 6; // All valid date collections from mock
        console.log('Expected count: 6 collections (all valid date collections)');
        console.log('Actual count:  ', collections6.length);
        console.log('Collections:   ', collections6.map(c => c.name.replace('collection_', '')).sort());
        console.log('✅ Test 6:', collections6.length === expectedCount ? 'PASSED' : 'FAILED');
    } catch (error) {
        console.log('❌ Test 6 FAILED with error:', error.message);
    }
    
    console.log('\n' + '='.repeat(80));
    console.log('🏁 UTC Compatibility Test Complete');
    console.log('='.repeat(80));
}

// Run the test
testUTCCompatibility().catch(console.error).finally(() => process.exit(0));
