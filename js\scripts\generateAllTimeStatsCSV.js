// node scripts/generateAllTimeStatsCSV.js --output-dir=path/to/dir

require('dotenv').config();
const fs = require('fs');
const path = require('path');
const db = require('../modules/db');
const Vessel = require('../models/Vessel');
const HomePort = require('../models/HomePort');
const { getSessionsByCoordinates, getLocationsCollections } = require('../utils/functions');
const ExcelJS = require('exceljs');
const workbook = new ExcelJS.Workbook();

function clearLine() {
    process.stdout.write("\r\x1b[K");
}

function writeProgress(message) {
    clearLine();
    process.stdout.write(message);
}

function writeLine(message) {
    clearLine();
    process.stdout.write(message + "\n");
}

async function generateAllTimeStatsCSV() {
    try {
        writeLine('🔍 Building vessel mapping...');
        const vessels = await Vessel.find({}, { unit_id: 1, onboard_vessel_id: 1, _id: 1, name: 1 });
        const vesselNames = {};
        vessels.forEach(vessel => {
            vesselNames[vessel._id.toString()] = vessel.name || vessel.onboard_vessel_id.toString();
        });
        writeLine(`✅ Found ${Object.keys(vesselNames).length} vessels.`);

        // Prepare stats containers
        const totalSmartmastsDistanceTraveled = {};
        const totalSensorsOnlineDuration = {};
        const totalVesselsDetectedbySensors = {};
        const totalVesselsSuperCategorized = {};
        const totalVesselsSubCategorized = {};
        // Add per-vessel category counters
        const perVesselSuperCategorized = {};
        const perVesselSubCategorized = {};

        // Process artifacts using a cursor (streaming, with projection)
        writeLine('🔍 Processing artifacts...');
        const artifactsCursor = db.qmai.db.collection('analysis_results').find({ vessel_presence: true }, { projection: { unit_id: 1, onboard_vessel_id: 1, super_category: 1, category: 1, det_nbbox_area: 1, vessel_presence: 1 } });
        let artifactCount = 0;
        for await (const a of artifactsCursor) {
            artifactCount++;
            if (!a.onboard_vessel_id) continue;
            let vesselId = a.onboard_vessel_id.toString();
            if (!vesselId) continue;
            if (!totalVesselsDetectedbySensors[vesselId]) totalVesselsDetectedbySensors[vesselId] = 0;
            totalVesselsDetectedbySensors[vesselId] += 1;
            if (a.super_category && a.det_nbbox_area >= 0.03) {
                if (!totalVesselsSuperCategorized[a.super_category]) totalVesselsSuperCategorized[a.super_category] = 0;
                totalVesselsSuperCategorized[a.super_category] += 1;
                if (!perVesselSuperCategorized[vesselId]) perVesselSuperCategorized[vesselId] = {};
                if (!perVesselSuperCategorized[vesselId][a.super_category]) perVesselSuperCategorized[vesselId][a.super_category] = 0;
                perVesselSuperCategorized[vesselId][a.super_category] += 1;
            }
            if (a.category && a.det_nbbox_area >= 0.03) {
                if (!totalVesselsSubCategorized[a.category]) totalVesselsSubCategorized[a.category] = 0;
                totalVesselsSubCategorized[a.category] += 1;
                if (!perVesselSubCategorized[vesselId]) perVesselSubCategorized[vesselId] = {};
                if (!perVesselSubCategorized[vesselId][a.category]) perVesselSubCategorized[vesselId][a.category] = 0;
                perVesselSubCategorized[vesselId][a.category] += 1;
            }
        }
        writeLine(`✅ Processed ${artifactCount} artifacts.`);

        // Process locations collection by collection, streaming, with projection
        writeLine('🔍 Processing location collections...');
        const collections = await getLocationsCollections(db.locationsRaw);
        writeLine(`Found ${collections.length} location collections.`);
        const LatLonSpherical = (await
            import ('geodesy/latlon-spherical.js')).default;
        // Fetch home ports with projection
        const homePorts = await HomePort.find({}, { lat: 1, lng: 1, _id: 0 });
        const stationaryDistance = 15; // meters
        let locCollectionIdx = 0;
        let totalLocations = 0;
        for (const collection of collections) {
            locCollectionIdx++;
            writeProgress(`Processing collection ${locCollectionIdx}/${collections.length}: ${collection} ...`);
            const cursor = collection.find({}, { projection: { onboardVesselId: 1, latitude: 1, longitude: 1, timestamp: 1 }, sort: { timestamp: 1 } });
            // Group locations by onboardVesselId in memory
            const vesselLocsMap = {};
            let locCount = 0;
            for await (const loc of cursor) {
                if (!loc.onboardVesselId) continue;
                const vesselId = loc.onboardVesselId.toString();
                if (!vesselLocsMap[vesselId]) vesselLocsMap[vesselId] = [];
                vesselLocsMap[vesselId].push(loc);
                locCount++;
            }
            totalLocations += locCount;
            writeLine(`✅ Processed collection ${locCollectionIdx}/${collections.length}: ${collection} (${locCount} locations)`);
            for (const vesselId of Object.keys(vesselLocsMap)) {
                const locations = vesselLocsMap[vesselId];
                const sessions = getSessionsByCoordinates(locations);
                let onlineDuration = 0;
                if (sessions.length !== 0) {
                    onlineDuration = sessions.reduce((sum, session) => sum += new Date(session[session.length - 1].timestamp).getTime() - new Date(session[0].timestamp).getTime(), 0);
                }
                totalSensorsOnlineDuration[vesselId] = (totalSensorsOnlineDuration[vesselId] || 0) + onlineDuration;

                // At-sea duration (filter out home ports)
                const seaLocations = locations.filter(loc => {
                    if (homePorts.some(c => new LatLonSpherical(c.lat, c.lng).distanceTo(new LatLonSpherical(loc.latitude, loc.longitude)) < 1000))
                        return false;
                    else
                        return true;
                });
                const seaSessions = getSessionsByCoordinates(seaLocations);
                let atSeaDuration = 0;
                if (seaSessions.length !== 0) {
                    atSeaDuration = seaSessions.reduce((sum, session) => sum += new Date(session[session.length - 1].timestamp).getTime() - new Date(session[0].timestamp).getTime(), 0);
                }
                // (If you want to output at-sea duration, you can add it to the Excel as well)

                // Distance travelled (excluding stationary, and only at sea)
                let distanceSum = 0;
                if (seaLocations.length >= 2) {
                    var totalDistance = 0;
                    seaLocations.forEach((loc, i) => {
                        const nextLoc = seaLocations[i + 1];
                        if (nextLoc) {
                            const distance = new LatLonSpherical(loc.latitude, loc.longitude).distanceTo(new LatLonSpherical(nextLoc.latitude, nextLoc.longitude));
                            if (distance >= stationaryDistance) totalDistance += distance;
                        }
                    });
                    distanceSum = totalDistance;
                }
                totalSmartmastsDistanceTraveled[vesselId] = (totalSmartmastsDistanceTraveled[vesselId] || 0) + distanceSum;
            }
        }
        writeLine(`✅ All location collections processed. Total locations: ${totalLocations}`);

        // Prepare two-row CSV header
        const mainHeaders = [
            'Vessel ID',
            'Vessel Name',
            'Miles Travelled',
            'Hours Online',
            'Vessels Detected',
        ];
        const superCategories = Object.keys(totalVesselsSuperCategorized);
        const subCategories = Object.keys(totalVesselsSubCategorized);
        // First header row: main columns, then 'Super Category' (spanning), then 'Category' (spanning)
        const headerRow1 = [
            ...mainHeaders,
            ...Array(superCategories.length).fill('Super Category'),
            ...Array(subCategories.length).fill('Category')
        ];
        // Second header row: empty for main columns, then actual super-category and category names
        const headerRow2 = [
            ...Array(mainHeaders.length).fill(''),
            ...superCategories,
            ...subCategories
        ];

        // Prepare CSV rows (per vessel)
        const allVesselIds = Object.keys(vesselNames);
        const rows = [headerRow1, headerRow2];
        for (const vesselId of allVesselIds) {
            const name = vesselNames[vesselId] || '';
            const milesTravelled = ((totalSmartmastsDistanceTraveled[vesselId] || 0) / 1609.34).toFixed(2); // meters to miles
            const hoursOnline = ((totalSensorsOnlineDuration[vesselId] || 0) / (1000 * 60 * 60)).toFixed(2); // ms to hours
            const vesselsDetected = totalVesselsDetectedbySensors[vesselId] || 0;
            const row = [
                vesselId,
                name,
                milesTravelled,
                hoursOnline,
                vesselsDetected,
            ];
            // Add super-category counts (per vessel)
            row.push(...superCategories.map(c => (perVesselSuperCategorized[vesselId] && perVesselSuperCategorized[vesselId][c] || 0)));
            // Add sub-category counts (per vessel)
            row.push(...subCategories.map(c => (perVesselSubCategorized[vesselId] && perVesselSubCategorized[vesselId][c] || 0)));
            rows.push(row);
        }

        // Calculate summary row (sum of all vessels)
        const summaryRow = [
            'ALL',
            'ALL VESSELS',
        ];
        // Sum numeric columns
        let sumMiles = 0,
            sumHours = 0,
            sumDetected = 0;
        let sumSuperCats = superCategories.map(() => 0);
        let sumSubCats = subCategories.map(() => 0);
        for (const vesselId of allVesselIds) {
            sumMiles += Number(((totalSmartmastsDistanceTraveled[vesselId] || 0) / 1609.34));
            sumHours += Number(((totalSensorsOnlineDuration[vesselId] || 0) / (1000 * 60 * 60)));
            sumDetected += totalVesselsDetectedbySensors[vesselId] || 0;
        }
        for (let i = 0; i < superCategories.length; ++i) {
            sumSuperCats[i] = totalVesselsSuperCategorized[superCategories[i]] || 0;
        }
        for (let i = 0; i < subCategories.length; ++i) {
            sumSubCats[i] = totalVesselsSubCategorized[subCategories[i]] || 0;
        }
        summaryRow.push(sumMiles.toFixed(2));
        summaryRow.push(sumHours.toFixed(2));
        summaryRow.push(sumDetected);
        summaryRow.push(...sumSuperCats);
        summaryRow.push(...sumSubCats);

        // Add a blank line and a header for the summary
        rows.push([]);
        rows.push(['Summary (Sum of All Vessels)']);
        rows.push(headerRow1);
        rows.push(headerRow2);
        rows.push(summaryRow);

        // --- Excel file generation ---
        writeLine('🔍 Generating Excel file...');
        const worksheet = workbook.addWorksheet('All Time Stats');

        // Write the two header rows for per-vessel section
        worksheet.addRow(headerRow1);
        worksheet.addRow(headerRow2);

        // Write per-vessel rows and accumulate totals for each column
        const perVesselRows = [];
        let totalMiles = 0,
            totalHours = 0,
            totalDetected = 0;
        let totalSuperCats = superCategories.map(() => 0);
        let totalSubCats = subCategories.map(() => 0);
        for (const vesselId of allVesselIds) {
            const name = vesselNames[vesselId] || '';
            const milesTravelled = ((totalSmartmastsDistanceTraveled[vesselId] || 0) / 1609.34).toFixed(2); // meters to miles
            const hoursOnline = ((totalSensorsOnlineDuration[vesselId] || 0) / (1000 * 60 * 60)).toFixed(2); // ms to hours
            const vesselsDetected = totalVesselsDetectedbySensors[vesselId] || 0;
            const row = [
                vesselId,
                name,
                milesTravelled,
                hoursOnline,
                vesselsDetected,
            ];
            // Add super-category counts (per vessel)
            const superCatCounts = superCategories.map(c => (perVesselSuperCategorized[vesselId] && perVesselSuperCategorized[vesselId][c] || 0));
            row.push(...superCatCounts);
            // Add sub-category counts (per vessel)
            const subCatCounts = subCategories.map(c => (perVesselSubCategorized[vesselId] && perVesselSubCategorized[vesselId][c] || 0));
            row.push(...subCatCounts);
            perVesselRows.push(row);
            // Accumulate totals
            totalMiles += parseFloat(milesTravelled);
            totalHours += parseFloat(hoursOnline);
            totalDetected += vesselsDetected;
            for (let i = 0; i < superCategories.length; ++i) totalSuperCats[i] += superCatCounts[i];
            for (let i = 0; i < subCategories.length; ++i) totalSubCats[i] += subCatCounts[i];
        }
        // Write per-vessel rows
        for (const row of perVesselRows) worksheet.addRow(row);
        // Add a 'Total' row at the end of the per-vessel section
        const totalRow = [
            'Total', '',
            totalMiles.toFixed(2),
            totalHours.toFixed(2),
            totalDetected,
            ...totalSuperCats,
            ...totalSubCats
        ];
        const totalRowObj = worksheet.addRow(totalRow);
        totalRowObj.font = { bold: true };

        // Add a blank row between sections
        worksheet.addRow([]);

        // Write the summary section (all sensors, ever)
        const allSensorsSummaryRow = worksheet.addRow(['All Sensors Summary']);
        allSensorsSummaryRow.font = { bold: true };
        worksheet.addRow(['How many miles have SmartMast travelled?', totalMiles.toFixed(2)]);
        worksheet.addRow(['How many hours have they been online?', totalHours.toFixed(2)]);
        worksheet.addRow(['How many vessels detected?', totalDetected]);
        worksheet.addRow([]); // Add empty line before 'Total Detections by Super-Category'
        const superCatHeaderRow = worksheet.addRow(['Total Detections by Super-Category']);
        superCatHeaderRow.font = { bold: true };
        for (let i = 0; i < superCategories.length; ++i) {
            worksheet.addRow([superCategories[i], totalSuperCats[i]]);
        }
        worksheet.addRow([]); // Add empty line before 'Total Detections by Category'
        const subCatHeaderRow = worksheet.addRow(['Total Detections by Category']);
        subCatHeaderRow.font = { bold: true };
        for (let i = 0; i < subCategories.length; ++i) {
            worksheet.addRow([subCategories[i], totalSubCats[i]]);
        }

        // Merge cells for Super Category and Category in the per-vessel section header row only
        const mainHeadersLen = mainHeaders.length;
        if (superCategories.length > 0) {
            worksheet.mergeCells(1, mainHeadersLen + 1, 1, mainHeadersLen + superCategories.length);
        }
        if (subCategories.length > 0) {
            worksheet.mergeCells(1, mainHeadersLen + superCategories.length + 1, 1, mainHeadersLen + superCategories.length + subCategories.length);
        }
        // Merge main header cells (single columns) for the per-vessel section
        for (let i = 1; i <= mainHeadersLen; ++i) {
            worksheet.mergeCells(1, i, 2, i);
        }

        // Style header rows for the per-vessel section
        worksheet.getRow(1).font = { bold: true };
        worksheet.getRow(2).font = { bold: true };
        worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };
        worksheet.getRow(2).alignment = { vertical: 'middle', horizontal: 'center' };

        // Autosize columns
        worksheet.columns.forEach(column => {
            let maxLength = 10;
            column.eachCell({ includeEmpty: true }, cell => {
                maxLength = Math.max(maxLength, (cell.value ? String(cell.value).length : 0));
            });
            column.width = maxLength + 2;
        });

        // Center align all numbered values (all columns except first two for per-vessel, total, and summary rows)
        // Per-vessel section: header is 1 and 2, data starts at row 3
        const perVesselStartRow = 3;
        const perVesselEndRow = perVesselStartRow + perVesselRows.length; // exclusive
        for (let i = perVesselStartRow; i < perVesselEndRow; ++i) {
            const row = worksheet.getRow(i);
            for (let j = 3; j <= row.cellCount; ++j) {
                row.getCell(j).alignment = { horizontal: 'center' };
            }
        }
        // 'Total' row (immediately after per-vessel rows)
        const totalRowIdx = perVesselEndRow;
        const totalRowObj2 = worksheet.getRow(totalRowIdx);
        for (let j = 3; j <= totalRowObj2.cellCount; ++j) {
            totalRowObj2.getCell(j).alignment = { horizontal: 'center' };
        }
        // Center align all value columns in summary/statistics section (all rows after totalRowIdx)
        worksheet.eachRow((row, rowNumber) => {
            if (rowNumber > totalRowIdx) {
                if (row.cellCount >= 2) {
                    for (let j = 2; j <= row.cellCount; ++j) {
                        row.getCell(j).alignment = { horizontal: 'center' };
                    }
                }
            }
        });

        // Parse --output-dir flag
        let saveDir = process.cwd();
        const outputDirFlag = process.argv.find(arg => arg.startsWith('--output-dir='));
        if (outputDirFlag) {
            const flagValue = outputDirFlag.split('=')[1];
            if (!flagValue || !fs.existsSync(flagValue) || !fs.statSync(flagValue).isDirectory()) {
                console.error('Error: The specified directory in --output-dir does not exist. Exiting.');
                process.exit(1);
            }
            saveDir = flagValue;
        }
        // Generate timestamp for filename
        const now = new Date();
        const pad = n => n.toString().padStart(2, '0');
        const timestamp = `${now.getFullYear()}${pad(now.getMonth() + 1)}${pad(now.getDate())}_${pad(now.getHours())}${pad(now.getMinutes())}${pad(now.getSeconds())}`;
        const excelPath = path.join(saveDir, `all_time_stats_${timestamp}.xlsx`);
        await workbook.xlsx.writeFile(excelPath);
        writeLine(`✅ All-time stats Excel file written to ${excelPath}`);
        process.exit(0);
    } catch (err) {
        console.error('Failed to generate all-time stats CSV:', err);
        process.exit(1);
    }
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once('open', resolve);
        db.qm.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmai.once('open', resolve);
        db.qmai.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    })
]).then(() => {
    generateAllTimeStatsCSV()
        .then(() => {
            writeLine('All-time stats script completed successfully');
            process.exit(0);
        })
        .catch(err => {
            console.error('All-time stats script failed:', err);
            process.exit(1);
        });
});