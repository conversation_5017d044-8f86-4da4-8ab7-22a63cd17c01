const mongoose = require('mongoose');
const db = require('../modules/db');
const User = require('./User');

const vesselSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
    },
    thumbnail_s3_key: {
        type: String,
        required: false,
        default: null,
    },
    thumbnail_compressed_s3_key: {
        type: String,
        required: false,
        default: null,
    },
    unit_id: {
        type: String,
        required: false,
        unique: true,
        sparse: true,
    },
    region_group_id: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
    },
    home_port_location: {
        type: Object,
        default: null,
    },
    is_active: {
        type: Boolean,
        required: true,
        default: true,
    },
    units_history: [
        {
            _id: false,
            unit_id: {
                type: String,
                required: true,
            },
            mount_timestamp: {
                type: Date,
                required: true,
            },
            unmount_timestamp: {
                type: Date,
                required: false,
            },
        },
    ],
    creation_timestamp: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
    created_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: User,
        required: true,
    },
});

vesselSchema.index({ unit_id: 1 });
vesselSchema.index({ home_port_location: "2dsphere" });

const Vessel = db.qmShared.model('Vessel', vesselSchema, 'vessels');

module.exports = Vessel;
