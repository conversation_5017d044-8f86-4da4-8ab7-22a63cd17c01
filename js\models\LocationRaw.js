// const { listThings } = require("../modules/awsIot")
const mongoose = require('mongoose');
const db = require("../modules/db");

let RawLocations = {}

const getLocationRawCollection = (month) => {
    const collection = month

    if (RawLocations[collection]) return RawLocations[collection]

    const RawLocationSchema = new mongoose.Schema({
        location: {
            type: {
                type: String,
                enum: ['Point'],
                required: true
            },
            coordinates: {
                type: [Number],
                required: true
            }
        },
        groundSpeed: { type: Number, required: true },
        isStationary: { type: Boolean, required: true },
        headingMotion: { type: Number, required: true },
        accuracyHeading: { type: Number, required: true },
        metadata: { type: Object, required: true },
        details: { type: Object, required: true },
        timestamp: { type: Date, required: true }
    }, { minimize: false, timeseries: { timeField: 'timestamp', metaField: 'metadata', granularity: 'seconds' } });

    RawLocationSchema.index({ timestamp: 1, 'metadata.onboardVesselId': 1 })
    RawLocationSchema.index({ 'metadata.onboardVesselId': 1 })

    RawLocations[collection] = db.locationsRaw.model(collection, RawLocationSchema, collection);

    return RawLocations[collection]
}

module.exports = {
    getLocationRawCollection
};


// db.getCollectionNames().forEach(function(c) {
//     if (c.endsWith("_location")) {
//       db[c].drop();
//     }
//   });