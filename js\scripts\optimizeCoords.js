require('dotenv').config()
const db = require("../modules/db");
const simplify = require('simplify-js');
const { getTerminalConfirmation, getTerminalInput } = require('../utils/functions');

const getCollectionNames = async () => {
    return (await db.qmLocations.listCollections()).map(c => c.name).filter(c => !c.startsWith('system.'))
}


const getSimplifiedCoords = (coords) => {

    // Step 1: Prepare points with a reference index
    const points = coords.map((c, i) => ({
        x: c.longitude,
        y: c.latitude,
        _index: i // store original index
    }));

    // Step 2: Simplify
    const simplified = simplify(points, 0.0001, true);

    // Step 3: Map back to original objects
    const result = simplified.map(p => coords[p._index]);

    return result;
}

const updateCoords = (coords, isOptimized) => {
    return coords.map(c => {
        const { onboardVesselId, latitude, longitude, unitId, metadata, ...rest } = c;

        if (isOptimized) {
            return {
                ...rest,
                location: {
                    type: "Point",
                    coordinates: [longitude, latitude]
                },
                metadata: {
                    onboardVesselId,
                    unitId
                }
            };
        } else {
            return {
                ...rest,
                location: {
                    type: "Point",
                    coordinates: [longitude, latitude]
                },
                metadata: {
                    onboardVesselId,
                    unitId
                },
                details: metadata || {}
            };
        }
    });
};

const getMonthRange = (month) => {
    const [year, monthNum] = month.split('-').map(Number);
    const startDate = new Date(Date.UTC(year, monthNum - 1, 1, 0, 0, 0, 0));
    const endDate = new Date(Date.UTC(year, monthNum, 0, 23, 59, 59, 999));
    return [startDate, endDate];
}

async function optimizeCoords() {
    const month = await getTerminalInput('Enter month (YYYY-MM): ')
    const dateRange = getMonthRange(month)
    console.log('Date Range', dateRange)
    console.log('Using Source DB', db.qmLocations.name)
    const collectionNames = await getCollectionNames()
    console.log('collectionNames.length', collectionNames.length)

    if ((await db.locationsOptimized.listCollections({ name: month })).find(c => c.name === month)) {
        const confirmation = await getTerminalConfirmation(`Collection ${month} already exists in ${db.locationsOptimized.name}, do you wish to drop? (y/n): `)
        if (!confirmation) {
            console.log('User did not confirm, exiting...');
            process.exit(0);
        }
        await db.locationsOptimized.collection(month).drop()
    }

    if ((await db.locationsRaw.listCollections({ name: month })).find(c => c.name === month)) {
        const confirmation = await getTerminalConfirmation(`Collection ${month} already exists in ${db.locationsRaw.name}, do you wish to drop? (y/n): `)
        if (!confirmation) {
            console.log('User did not confirm, exiting...');
            process.exit(0);
        }
        await db.locationsRaw.collection(month).drop()
    }

    console.log('fetching coordinates...')
    let allCoords = (await Promise.all(collectionNames.map(async cName => {
        const unitId = cName.split('_location')[0]
        const collection = db.qmLocations.collection(cName)
        const coords = (await collection.find({
            timestamp: {
                $gte: dateRange[0],
                $lte: dateRange[1]
            }
        }).toArray()).map(c => {
            return {
                ...c,
                unitId
            }
        })
        return coords
    }))).flat().sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())

    console.log('allCoords', allCoords.length)

    const ditinctVesselIds = [...new Set(allCoords.map(c => c.onboardVesselId?.toString()))].filter(vesselId => vesselId)
    console.log('ditinctVesselIds', ditinctVesselIds.length)

    let optCoords = []
    ditinctVesselIds.forEach(vesselId => {
        const coords = allCoords.filter(c => c.onboardVesselId?.toString() === vesselId).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
        const optimizedCoords = getSimplifiedCoords(coords)
        // console.log('optimizedCoords', vesselId, optimizedCoords.length, 'vs', coords.length)
        optCoords.push(...optimizedCoords)
    })

    console.log('optCoords', optCoords.length, 'vs', allCoords.length)

    const updatedCoords = updateCoords(optCoords, true)
    const updatedCoordsRaw = updateCoords(allCoords, false)

    // clean memory
    allCoords = null
    optCoords = null
    console.log('cleaned memory')

    const confirmation = await getTerminalConfirmation(`Creating collection ${month} in db ${db.locationsOptimized.name} and inserting ${updatedCoords.length} records, continue? (y/n): `)
    if (!confirmation) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    const confirmation2 = await getTerminalConfirmation(`Creating collection ${month} in db ${db.locationsRaw.name} and inserting ${updatedCoordsRaw.length} records, continue? (y/n): `)
    if (!confirmation2) {
        console.log('User did not confirm, exiting...');
        process.exit(0);
    }

    const optimizedCollection = await db.locationsOptimized.createCollection(month, { timeseries: { timeField: 'timestamp', metaField: 'metadata', granularity: 'seconds' } })

    await optimizedCollection.createIndex({ timestamp: 1, 'metadata.onboardVesselId': 1 })
    await optimizedCollection.createIndex({ 'metadata.onboardVesselId': 1 })

    await optimizedCollection.insertMany(updatedCoords)

    const rawCollection = await db.locationsRaw.createCollection(month, { timeseries: { timeField: 'timestamp', metaField: 'metadata', granularity: 'seconds' } })

    await rawCollection.createIndex({ timestamp: 1, 'metadata.onboardVesselId': 1 })
    await rawCollection.createIndex({ 'metadata.onboardVesselId': 1 })

    await rawCollection.insertMany(updatedCoordsRaw)

    console.log('Success')
    process.exit(0)
}

Promise.all([
    new Promise((resolve, reject) => {
        db.qmLocations.once('open', resolve);
        db.qmLocations.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.locationsOptimized.once('open', resolve);
        db.locationsOptimized.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.locationsRaw.once('open', resolve);
        db.locationsRaw.on('error', reject);
    })
]).then(() => {
    setTimeout(() => {
        optimizeCoords()
    }, 1000);
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});